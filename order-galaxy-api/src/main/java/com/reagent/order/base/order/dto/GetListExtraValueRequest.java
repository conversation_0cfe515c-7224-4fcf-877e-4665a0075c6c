package com.reagent.order.base.order.dto;

import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 查询List类型extraValue请求DTO
 */
public class GetListExtraValueRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单ID列表，最多200个")
    private List<Integer> orderIds;

    @ModelProperty(value = "List类型扩展键", enumLink = "com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum")
    private List<Integer> extraKeys;

    public List<Integer> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(List<Integer> orderIds) {
        this.orderIds = orderIds;
    }

    public List<Integer> getExtraKeys() {
        return extraKeys;
    }

    public void setExtraKeys(List<Integer> extraKeys) {
        this.extraKeys = extraKeys;
    }

    @Override
    public String toString() {
        return "GetListExtraValueRequest{" +
                "orderIds=" + orderIds +
                ", extraKeys=" + extraKeys +
                '}';
    }
}

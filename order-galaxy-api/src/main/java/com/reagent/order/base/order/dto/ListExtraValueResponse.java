package com.reagent.order.base.order.dto;


import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * List类型extraValue响应DTO
 */
public class ListExtraValueResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单ID到extraKey->List的映射")
    private Map<Integer, Map<Integer, List<String>>> orderExtraMap;



    public Map<Integer, Map<Integer, List<String>>> getOrderExtraMap() {
        return orderExtraMap;
    }

    public void setOrderExtraMap(Map<Integer, Map<Integer, List<String>>> orderExtraMap) {
        this.orderExtraMap = orderExtraMap;
    }



    @Override
    public String toString() {
        return "ListExtraValueResponse{" +
                "orderExtraMap=" + orderExtraMap +
                '}';
    }
}

package com.reagent.order.base.order.dto;


import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 追加List类型extraValue请求DTO
 */
public class AppendListExtraValueRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单ID")
    private Integer orderId;

    @ModelProperty("扩展键（12-ACCEPT_APPROVE_USERS或42-ORDER_TAG）")
    private Integer extraKey;

    @ModelProperty("要追加的值列表")
    private List<String> appendValues;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getExtraKey() {
        return extraKey;
    }

    public void setExtraKey(Integer extraKey) {
        this.extraKey = extraKey;
    }

    public List<String> getAppendValues() {
        return appendValues;
    }

    public void setAppendValues(List<String> appendValues) {
        this.appendValues = appendValues;
    }

    @Override
    public String toString() {
        return "AppendListExtraValueRequest{" +
                "orderId=" + orderId +
                ", extraKey=" + extraKey +
                ", appendValues=" + appendValues +
                '}';
    }
}

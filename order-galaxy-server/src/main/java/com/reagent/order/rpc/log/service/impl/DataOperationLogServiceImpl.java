package com.reagent.order.rpc.log.service.impl;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.reagent.order.base.log.dto.DataOperationLogDTO;
import com.reagent.order.base.log.mapper.DataOperationLogMapper;
import com.reagent.order.base.log.request.DataOperationLogListRequest;
import com.reagent.order.base.log.service.DataOperationLogService;
import com.reagent.order.base.log.translator.DataOperationLogTranslator;
import com.reagent.order.utils.PageResponseUtils;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/7 14:57
 * @description
 */
@MSharpService
public class DataOperationLogServiceImpl implements DataOperationLogService {

    @Resource
    private DataOperationLogMapper dataOperationLogMapper;

    @Override
    @ServiceLog(description = "插入OMS异常数据修正日志", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> insertLog(DataOperationLogDTO dataOperationLogDTO) {
        Preconditions.notNull(dataOperationLogDTO, "请求参数不可空");
        int count = dataOperationLogMapper.insertSelective(DataOperationLogTranslator.dto2Do(dataOperationLogDTO));
        return RemoteResponse.success(count > 0);
    }

    @Override
    @ServiceLog(description = "批量插入OMS异常数据修正日志", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> batchInsertLog(List<DataOperationLogDTO> dataOperationLogDTOList) {
        Preconditions.notEmpty(dataOperationLogDTOList, "请求参数不可空");
        int count = dataOperationLogMapper.batchInsertSelective(dataOperationLogDTOList.stream().map(DataOperationLogTranslator::dto2Do).collect(Collectors.toList()));
        return RemoteResponse.success(true);
    }

    @Override
    @ServiceLog(description = "查询OMS异常数据修正日志")
    public PageableResponse<List<DataOperationLogDTO>> listByParams(DataOperationLogListRequest dataOperationLogListRequest) {
        Preconditions.notNull(dataOperationLogListRequest, "请求参数不可空");
        Integer pageNo = dataOperationLogListRequest.getPageNo();
        Integer pageSize = dataOperationLogListRequest.getPageSize();
        Preconditions.notNull(pageNo, "页号不能为空");
        Preconditions.notNull(pageSize, "分页大小不可空");
        return PageResponseUtils.pageInvoke(
                () -> dataOperationLogMapper.listDataOperationLog(dataOperationLogListRequest),
                DataOperationLogTranslator::do2Dto,
                pageNo,
                pageSize);
    }
}

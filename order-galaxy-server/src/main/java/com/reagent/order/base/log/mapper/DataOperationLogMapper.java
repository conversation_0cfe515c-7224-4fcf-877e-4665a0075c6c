package com.reagent.order.base.log.mapper;

import com.reagent.order.base.log.request.DataOperationLogListRequest;
import com.reagent.order.base.log.model.DataOperationLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/12/7 14:22
 * @description
 */
public interface DataOperationLogMapper {

    /**
     * 插入日志
     * @param record 日志数据
     * @return 插入行数
     */
    int insertSelective(DataOperationLog record);

    /**
     * 批量插入日志
     * @param recordList 日志记录
     * @return 插入行数
     */
    int batchInsertSelective(@Param("list") List<DataOperationLog> recordList);

    /**
     * 查找日志数据
     * @param dataOperationLogListRequest 日志数据请求体
     * @return 日志数据
     */
    List<DataOperationLog> listDataOperationLog(DataOperationLogListRequest dataOperationLogListRequest);

    Integer selectMinId();

    Integer selectMaxId();

    /**
     * 根据id范围查找数据
     *
     * @param minId
     * @param maxId
     * @return
     */
    List<DataOperationLog> listDataOperationLogBetweenId(@Param("minId") Integer minId, @Param("maxId") Integer maxId);

    /**
     * 更新PreDesc字段值
     * @param dataOperationLogList
     * @return
     */
    @Deprecated
    int batchUpdatePreDesc(List<DataOperationLog> dataOperationLogList);
}
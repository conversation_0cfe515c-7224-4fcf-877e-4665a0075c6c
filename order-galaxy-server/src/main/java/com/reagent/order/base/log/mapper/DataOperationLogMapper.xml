<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.log.mapper.DataOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.log.model.DataOperationLog">
    <!--@mbg.generated-->
    <!--@Table data_operation_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="fix_reason" jdbcType="VARCHAR" property="fixReason" />
    <result column="operation" jdbcType="VARCHAR" property="operation" />
    <result column="operator_guid" jdbcType="VARCHAR" property="operatorGuid" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="operation_type" jdbcType="INTEGER" property="operationType" />
    <result column="prev_status" jdbcType="INTEGER" property="prevStatus" />
    <result column="approve_number" jdbcType="VARCHAR" property="approveNumber" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="pre_desc" jdbcType="VARCHAR" property="preDesc" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_no, fix_reason, `operation`, operator_guid, operator_name, org_id, org_name, 
    operation_type, prev_status, approve_number, created_time, updated_time, operate_time, pre_desc
  </sql>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.log.model.DataOperationLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into data_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="fixReason != null">
        fix_reason,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="operatorGuid != null">
        operator_guid,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="prevStatus != null">
        prev_status,
      </if>
      <if test="approveNumber != null">
        approve_number,
      </if>
      <if test="operateTime != null">
        operate_time,
      </if>
      <if test="preDesc != null">
        pre_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="fixReason != null">
        #{fixReason,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=VARCHAR},
      </if>
      <if test="operatorGuid != null">
        #{operatorGuid,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=INTEGER},
      </if>
      <if test="prevStatus != null">
        #{prevStatus,jdbcType=INTEGER},
      </if>
      <if test="approveNumber != null">
        #{approveNumber,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preDesc != null">
        #{preDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id">
    <!--@mbg.generated-->
    insert into data_operation_log(
        order_no,
        fix_reason,
        `operation`,
        operator_guid,
        operator_name,
        org_id,
        org_name,
        operation_type,
        prev_status,
        approve_number,
        operate_time,
        pre_desc)
    values
    <foreach collection="list" item="element" index="index" separator=",">
      (#{element.orderNo,jdbcType=VARCHAR},
      #{element.fixReason,jdbcType=VARCHAR},
      #{element.operation,jdbcType=INTEGER},
      #{element.operatorGuid,jdbcType=VARCHAR},
      #{element.operatorName,jdbcType=VARCHAR},
      #{element.orgId,jdbcType=INTEGER},
      #{element.orgName,jdbcType=VARCHAR},
      #{element.operationType,jdbcType=INTEGER},
      <choose>
        <when test="element.prevStatus != null ">
          #{element.prevStatus,jdbcType=INTEGER},
        </when>
        <otherwise>
          -99,
        </otherwise>
      </choose>
      <choose>
        <when test="element.approveNumber != null ">
          #{element.approveNumber,jdbcType=VARCHAR},
        </when>
        <otherwise>
          "",
        </otherwise>
      </choose>
      #{element.operateTime,jdbcType=TIMESTAMP},
      <choose>
        <when test="element.preDesc != null ">
          #{element.preDesc,jdbcType=VARCHAR}
        </when>
        <otherwise>
          ""
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>

  <select id="listDataOperationLog" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from data_operation_log
    <where>
      <if test="orgId != null">
        and org_id =  #{orgId,jdbcType=INTEGER}
      </if>

      <if test="orderNo != null">
        and order_no  = #{orderNo,jdbcType=VARCHAR}
      </if>

      <if test="operatorName != null">
        and operator_name = #{operatorName,jdbcType=VARCHAR}
      </if>

      <if test="approveNumber != null">
        and approve_number = #{approveNumber,jdbcType=VARCHAR}
      </if>

      <if test="starTime != null">
        and operate_time > #{starTime,jdbcType=TIMESTAMP}
      </if>

      <if test="endTime != null">
        and operate_time <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    order by operate_time desc
  </select>

  <select id="selectMinId" resultType="int">
    select MIN(id)
    from data_operation_log
  </select>

  <select id="selectMaxId" resultType="int">
    select MAX(id)
    from data_operation_log
  </select>

  <select id="listDataOperationLogBetweenId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from data_operation_log
    <where> id <![CDATA[>=]]> #{minId,jdbcType=INTEGER}
      and id <![CDATA[<]]> #{maxId, jdbcType=INTEGER}
    </where>
  </select>


  <update id="batchUpdatePreDesc">
    <foreach item="item" index="index" collection="list"
             open="" separator=";">
      update data_operation_log
      <set>
        pre_desc = #{item.preDesc,jdbcType=VARCHAR},
      </set>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>

</mapper>
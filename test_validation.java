// 简单测试验证我们的修改逻辑
public class TestValidation {
    
    // 模拟OrderExtraEnum
    enum TestOrderExtraEnum {
        ACCEPT_APPROVE_USERS(12, "有权限验收审批的人", java.util.List.class),
        ORDER_TAG(42, "订单标签", java.util.List.class),
        SECOND_RECEIVER_NAME(1, "第二验收人", String.class);
        
        private final Integer value;
        private final String desc;
        private final Class<?> dataTypeClass;
        
        TestOrderExtraEnum(Integer value, String desc, Class<?> dataTypeClass) {
            this.value = value;
            this.desc = desc;
            this.dataTypeClass = dataTypeClass;
        }
        
        public Integer getValue() { return value; }
        public Class<?> getDataTypeClass() { return dataTypeClass; }
        
        public static TestOrderExtraEnum getByValue(Integer value) {
            for (TestOrderExtraEnum e : values()) {
                if (e.value.equals(value)) return e;
            }
            return null;
        }
    }
    
    // 测试我们的校验逻辑
    public static void testValidation() {
        // 测试List类型的extraKey - 应该通过
        TestOrderExtraEnum enum12 = TestOrderExtraEnum.getByValue(12);
        System.out.println("extraKey 12 is List type: " + 
            java.util.List.class.equals(enum12.getDataTypeClass()));
        
        TestOrderExtraEnum enum42 = TestOrderExtraEnum.getByValue(42);
        System.out.println("extraKey 42 is List type: " + 
            java.util.List.class.equals(enum42.getDataTypeClass()));
        
        // 测试非List类型的extraKey - 应该失败
        TestOrderExtraEnum enum1 = TestOrderExtraEnum.getByValue(1);
        System.out.println("extraKey 1 is List type: " + 
            java.util.List.class.equals(enum1.getDataTypeClass()));
        
        // 测试不存在的extraKey
        TestOrderExtraEnum enumNull = TestOrderExtraEnum.getByValue(999);
        System.out.println("extraKey 999 exists: " + (enumNull != null));
    }
    
    public static void main(String[] args) {
        testValidation();
    }
}
